package yq.bus.websocket;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.websocket.server.ServerEndpoint;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.AbstractWebSocketHandler;
import yq.bus.domain.vo.BusVehicleLocationVo;
import yq.bus.enums.SessionType;
import yq.bus.holder.BusWebSocketSessionHolder;
import yq.bus.service.IBusVehicleService;
import yq.bus.utils.BusWebSocketUtils;
import yq.common.websocket.utils.WebSocketUtils;
import javax.annotation.Resource;


/**
 * 后台车辆位置WebSocket处理器
 *
 * <AUTHOR>
 * @date 2024-08-19
 */
@Component
@ServerEndpoint("/backend/location/{vehicleId}") // 定义WebSocket的URL路径，包含路径参数车辆ID
public class BusBackendVehicleLocationHandler extends AbstractWebSocketHandler {

    @Resource
    private IBusVehicleService busVehicleService;

    /**
     * 连接建立后
     */
    @Override
    public void afterConnectionEstablished(WebSocketSession session) {
        Long pathParam = BusWebSocketUtils.getPathParam(session);
        BusWebSocketSessionHolder.addSession(SessionType.BACKEND_VEHICLE_LOCATION, pathParam, session);

        // 获取车辆当前位置
        BusVehicleLocationVo location = busVehicleService.getVehicleLocation(pathParam);
        if (location != null) {
            try {
                WebSocketUtils.sendMessage(session, new ObjectMapper().writeValueAsString(location));
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * 连接错误
     */
    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) {
        // 处理WebSocket传输错误
        BusWebSocketSessionHolder.removeSession(SessionType.BACKEND_VEHICLE_LOCATION, session);
    }

    /**
     * 连接关闭
     */
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        BusWebSocketSessionHolder.removeSession(SessionType.BACKEND_VEHICLE_LOCATION, session);
    }

    /**
     * 是否支持处理部分消息
     */
    @Override
    public boolean supportsPartialMessages() {
        return false;
    }
}
